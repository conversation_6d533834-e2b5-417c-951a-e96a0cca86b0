using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;
using RxjhServer;

namespace HeroYulgang.RxjhServer.AOI
{
    /// <summary>
    /// Extension methods để tích hợp AOI system với các class hiện có
    /// Cung cấp wrapper methods để thay thế dần các hàm cũ
    /// </summary>
    public static class AOIExtensions
    {
        /// <summary>
        /// Wrapper cho GetTheReviewRangePlayers sử dụng AOI system
        /// </summary>
        public static void GetTheReviewRangePlayersAOI(this Players player, bool skipBS = false)
        {
            try
            {
                if (player.CurrentZone != null && player.CurrentZone.IsCrossServer && !skipBS)
                {
                    // Sử dụng cross-server logic hiện có
                    player.GetTheReviewRangePlayersCrossServer();
                    return;
                }

                if (player.PlayList == null || player.Client.TreoMay)
                {
                    return;
                }

                // Sử dụng AOI system để lấy nearby players
                var nearbyPlayers = AOIManager.Instance.GetNearbyPlayers(player, AOIConfig.DefaultPlayerRange);

                // Xử lý players hiện tại trong PlayList
                var currentPlayerIds = player.PlayList.Keys.ToHashSet();
                var nearbyPlayerIds = nearbyPlayers.Select(p => (World.ServerID, p.SessionID)).ToHashSet();

                // Xóa players không còn trong phạm vi
                var playersToRemove = currentPlayerIds.Except(nearbyPlayerIds).ToList();
                foreach (var playerId in playersToRemove)
                {
                    if (player.PlayList.TryGetValue(playerId, out var playerToRemove))
                    {
                        player.PlayList.Remove(playerId);
                        player.DiChuyen_RaKhoi_BanDo(player, playerToRemove);
                        playerToRemove.PlayList?.Remove((World.ServerID, player.SessionID));
                        playerToRemove.DiChuyen_RaKhoi_BanDo(playerToRemove, player);
                    }
                }

                // Thêm players mới vào phạm vi
                foreach (var nearbyPlayer in nearbyPlayers)
                {
                    var playerId = (World.ServerID, nearbyPlayer.SessionID);

                    if (!player.PlayList.ContainsKey(playerId) &&
                        player.NhanVatToaDo_BanDo == nearbyPlayer.NhanVatToaDo_BanDo)
                    {
                        player.PlayList.Add(playerId, nearbyPlayer);
                        player.UpdateCharacterData(nearbyPlayer);
                    }

                    if (!nearbyPlayer.PlayList.ContainsKey((World.ServerID, player.SessionID)))
                    {
                        nearbyPlayer.PlayList.Add((World.ServerID, player.SessionID), player);
                        nearbyPlayer.UpdateCharacterData(player);
                    }

                    // Xử lý GM mode và stealth
                    HandlePlayerVisibilityEffects(player, nearbyPlayer);

                    // Xử lý Đại Chiến Hồn nếu cần
                    if (player.NhanVatToaDo_BanDo == 40101)
                    {
                        player.HandleDaiChienHon(nearbyPlayer);
                    }
                }

                if (AOIConfig.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug,
                        $"AOI: Updated {nearbyPlayers.Count} nearby players for {player.CharacterName}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI: Error in GetTheReviewRangePlayersAOI for {player.CharacterName}: {ex.Message}");

                // Fallback to original method
                player.GetTheReviewRangePlayers(skipBS);
            }
        }

        /// <summary>
        /// Wrapper cho GetReviewScopeNpc sử dụng AOI system
        /// </summary>
        public static void GetReviewScopeNpcAOI(this Players player, bool skipBS = false)
        {
            try
            {
                if (player.CurrentZone != null && player.CurrentZone.IsCrossServer && !skipBS)
                {
                    // Sử dụng cross-server logic hiện có
                    player.GetReviewScopeNpcCrossServer();
                    return;
                }

                if (player.NpcList == null || player.Client.TreoMay)
                {
                    return;
                }

                // Sử dụng AOI system để lấy nearby NPCs
                var nearbyNPCs = AOIManager.Instance.GetNearbyNPCs(player, AOIConfig.DefaultNPCRange);

                Dictionary<int, NpcClass> npcsToAdd = new();
                Dictionary<int, NpcClass> npcsToRemove = new();

                // Tìm NPCs cần xóa (không còn trong phạm vi)
                var currentNPCIds = player.NpcList.Keys.ToHashSet();
                var nearbyNPCIds = nearbyNPCs.Select(npc => npc.NPC_SessionID).ToHashSet();

                foreach (var npcId in currentNPCIds.Except(nearbyNPCIds))
                {
                    if (player.NpcList.TryGetValue(npcId, out var npcToRemove))
                    {
                        player.NpcList.Remove(npcId);
                        npcsToRemove[npcId] = npcToRemove;

                        if (player.Client != null && npcToRemove.Contains(player))
                        {
                            npcToRemove.PlayList_Remove(player);
                        }
                    }
                }

                // Thêm NPCs mới vào phạm vi
                foreach (var nearbyNPC in nearbyNPCs)
                {
                    if (!player.NpcList.ContainsKey(nearbyNPC.NPC_SessionID))
                    {
                        player.NpcList.Add(nearbyNPC.NPC_SessionID, nearbyNPC);
                        nearbyNPC.PlayList_Add(player);
                        npcsToAdd[nearbyNPC.NPC_SessionID] = nearbyNPC;
                    }
                }

                // Gửi updates tới client
                if (npcsToRemove.Count > 0)
                {
                    NpcClass.UpdateNPC_DeXoaSoLieu(npcsToRemove, player);
                }

                if (npcsToAdd.Count > 0)
                {
                    // Gọi UpdateNPCSoLieu cho từng NPC riêng lẻ vì đây là instance method
                    foreach (var npc in npcsToAdd.Values)
                    {
                        npc.UpdateNPCSoLieu(player);
                    }
                }

                if (AOIConfig.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug,
                        $"AOI: Updated NPCs for {player.CharacterName} - Added: {npcsToAdd.Count}, Removed: {npcsToRemove.Count}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI: Error in GetReviewScopeNpcAOI for {player.CharacterName}: {ex.Message}");

                // Fallback to original method
                player.GetReviewScopeNpc(skipBS);
            }
        }

        /// <summary>
        /// Wrapper cho ThuThap_VatPham_Drop_PhamVi sử dụng AOI system
        /// </summary>
        public static void ThuThap_VatPham_Drop_PhamViAOI(this Players player)
        {
            try
            {
                if (player.ListOfGroundItems == null)
                {
                    return;
                }

                // Sử dụng AOI system để lấy nearby items
                var nearbyItems = AOIManager.Instance.GetNearbyItems(player, AOIConfig.DefaultItemRange);

                // Xử lý items hiện tại
                var itemsToRemove = new List<long>();

                // Kiểm tra items hiện có trong list
                foreach (var existingItem in player.ListOfGroundItems.Values.ToList())
                {
                    if (!World.ItmeTeM.ContainsKey(existingItem.id))
                    {
                        itemsToRemove.Add(existingItem.id);
                        player.ListOfGroundItems.Remove(existingItem.id);
                    }
                }

                // Thêm items mới vào phạm vi
                foreach (var nearbyItem in nearbyItems)
                {
                    if (!player.ListOfGroundItems.ContainsKey(nearbyItem.id))
                    {
                        player.ListOfGroundItems.Add(nearbyItem.id, nearbyItem);
                        nearbyItem.PlayList.Add(player.SessionID, player);

                        // Gửi thông tin item tới client
                        player.SendGroundItemData(nearbyItem);
                    }
                }

                // Xóa items không còn trong phạm vi
                var currentItemIds = player.ListOfGroundItems.Keys.Select(k => (long)k).ToList();
                var nearbyItemIds = new HashSet<long>(nearbyItems.Select(item => item.id));

                foreach (var itemId in currentItemIds.Where(id => !nearbyItemIds.Contains(id)))
                {
                    if (player.ListOfGroundItems.TryGetValue(itemId, out var itemToRemove))
                    {
                        player.ListOfGroundItems.Remove(itemId);
                        itemToRemove.PlayList.Remove(player.SessionID);

                        // Gửi lệnh xóa item khỏi client
                        player.SendRemoveGroundItemData(itemToRemove);
                    }
                }

                if (AOIConfig.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug,
                        $"AOI: Updated {nearbyItems.Count} nearby items for {player.CharacterName}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI: Error in ThuThap_VatPham_Drop_PhamViAOI for {player.CharacterName}: {ex.Message}");

                // Fallback to original method
                player.ThuThap_VatPham_Drop_PhamVi();
            }
        }

        /// <summary>
        /// Cập nhật vị trí player trong AOI system
        /// </summary>
        public static void UpdateAOIPosition(this Players player)
        {
            try
            {
                AOIManager.Instance.UpdatePlayerPosition(player);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI: Error updating position for {player.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Thêm player vào AOI system khi login
        /// </summary>
        public static void JoinAOI(this Players player)
        {
            try
            {
                AOIManager.Instance.AddPlayer(player);

                if (AOIConfig.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, $"AOI: Player {player.CharacterName} joined AOI system");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI: Error adding player {player.CharacterName} to AOI: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa player khỏi AOI system khi logout
        /// </summary>
        public static void LeaveAOI(this Players player)
        {
            try
            {
                AOIManager.Instance.RemovePlayer(player);

                if (AOIConfig.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, $"AOI: Player {player.CharacterName} left AOI system");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI: Error removing player {player.CharacterName} from AOI: {ex.Message}");
            }
        }

        /// <summary>
        /// Refresh toàn bộ AOI cho player (players, NPCs, items)
        /// </summary>
        public static void RefreshAOI(this Players player)
        {
            try
            {
                player.GetTheReviewRangePlayersAOI();
                player.GetReviewScopeNpcAOI();
                player.ThuThap_VatPham_Drop_PhamViAOI();

                if (AOIConfig.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, $"AOI: Refreshed all AOI data for {player.CharacterName}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error,
                    $"AOI: Error refreshing AOI for {player.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý hiệu ứng visibility (GM mode, stealth, etc.)
        /// </summary>
        private static void HandlePlayerVisibilityEffects(Players viewer, Players target)
        {
            try
            {
                if (target.GMMode != 0 && target.HinhThuc_TangHinh != 0)
                {
                    target.HinhThuc_TangHinh = 1;
                    target.CheDo_TangHinh(1);
                }

                if (viewer.GMMode != 0 && viewer.HinhThuc_TangHinh != 0)
                {
                    viewer.HinhThuc_TangHinh = 1;
                    viewer.CheDo_TangHinh(1);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error handling visibility effects: {ex.Message}");
            }
        }

        /// <summary>
        /// Thêm NPC vào AOI system
        /// </summary>
        public static void JoinAOI(this NpcClass npc)
        {
            try
            {
                AOIManager.Instance.AddNPC(npc);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error adding NPC {npc.FLD_PID} to AOI: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa NPC khỏi AOI system
        /// </summary>
        public static void LeaveAOI(this NpcClass npc)
        {
            try
            {
                AOIManager.Instance.RemoveNPC(npc);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error removing NPC {npc.FLD_PID} from AOI: {ex.Message}");
            }
        }

        /// <summary>
        /// Thêm item vào AOI system
        /// </summary>
        public static void JoinAOI(this X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                AOIManager.Instance.AddItem(item);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error adding item {item.id} to AOI: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa item khỏi AOI system
        /// </summary>
        public static void LeaveAOI(this X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                AOIManager.Instance.RemoveItem(item);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error removing item {item.id} from AOI: {ex.Message}");
            }
        }

        /// <summary>
        /// Helper method để gửi thông tin ground item tới client
        /// </summary>
        private static void SendGroundItemData(this Players player, X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                // Sử dụng logic hiện có để gửi item data
                // Có thể cần implement dựa trên cấu trúc packet hiện có
                if (player.Client != null && !player.Client.TreoMay)
                {
                    // TODO: Implement item packet sending logic
                    // Tạm thời để trống, sẽ implement sau khi có thêm thông tin về packet structure
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error sending ground item data: {ex.Message}");
            }
        }

        /// <summary>
        /// Helper method để gửi lệnh xóa ground item khỏi client
        /// </summary>
        private static void SendRemoveGroundItemData(this Players player, X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                // Sử dụng logic hiện có để xóa item khỏi client
                // Có thể cần implement dựa trên cấu trúc packet hiện có
                if (player.Client != null && !player.Client.TreoMay)
                {
                    // TODO: Implement item removal packet sending logic
                    // Tạm thời để trống, sẽ implement sau khi có thêm thông tin về packet structure
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error sending remove ground item data: {ex.Message}");
            }
        }

        /// <summary>
        /// Kiểm tra xem có nên sử dụng AOI system hay không
        /// </summary>
        public static bool ShouldUseAOI()
        {
            // Có thể thêm logic để enable/disable AOI system
            // Ví dụ: kiểm tra config, số lượng players, etc.
            return true;
        }

        /// <summary>
        /// Lấy thống kê AOI cho debugging
        /// </summary>
        public static string GetAOIStats()
        {
            try
            {
                return AOIManager.Instance.GetOverallStats();
            }
            catch (Exception ex)
            {
                return $"Error getting AOI stats: {ex.Message}";
            }
        }
    }
}
