namespace HeroYulgang.RxjhServer.AOI
{
    /// <summary>
    /// C<PERSON>u hình cho hệ thống Area of Interest
    /// </summary>
    public static class AOIConfig
    {
        /// <summary>
        /// Kích thước mỗi grid cell (đơn vị: game units)
        /// </summary>
        public static int GridCellSize { get; set; } = 200;

        /// <summary>
        /// Phạm vi mặc định để tìm kiếm players xung quanh
        /// </summary>
        public static int DefaultPlayerRange { get; set; } = 400;

        /// <summary>
        /// Phạm vi mặc định để tìm kiếm NPCs xung quanh
        /// </summary>
        public static int DefaultNPCRange { get; set; } = 400;

        /// <summary>
        /// Phạm vi mặc định để tìm kiếm items xung quanh
        /// </summary>
        public static int DefaultItemRange { get; set; } = 400;

        /// <summary>
        /// Số lượng cells tối đa cần kiểm tra xung quanh (3x3 grid)
        /// </summary>
        public static int MaxNeighborCells { get; set; } = 9;

        /// <summary>
        /// C<PERSON> bật logging debug cho AOI system không
        /// </summary>
        public static bool EnableDebugLogging { get; set; } = true;

        /// <summary>
        /// Thời gian cache kết quả AOI (milliseconds)
        /// </summary>
        public static int CacheTimeoutMs { get; set; } = 100;

        /// <summary>
        /// Có sử dụng dirty flagging để tối ưu performance không
        /// </summary>
        public static bool UseDirtyFlagging { get; set; } = true;

        /// <summary>
        /// Có sử dụng batch updates không
        /// </summary>
        public static bool UseBatchUpdates { get; set; } = true;

        /// <summary>
        /// Kích thước batch tối đa cho updates
        /// </summary>
        public static int MaxBatchSize { get; set; } = 50;
    }
}
