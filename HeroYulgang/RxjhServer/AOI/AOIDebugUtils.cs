using System;
using System.Collections.Generic;
using HeroYulgang.Helpers;
using RxjhServer;

namespace HeroYulgang.RxjhServer.AOI
{
    /// <summary>
    /// Utilities để debug và kiểm tra hệ thống AOI
    /// </summary>
    public static class AOIDebugUtils
    {
        /// <summary>
        /// Debug chi tiết AOI cho một player
        /// </summary>
        public static void DebugPlayerAOI(Players player)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"=== AOI Debug for {player.CharacterName} ===");

                // Thông tin cơ bản
                LogHelper.WriteLine(LogLevel.Info,
                    $"Player: {player.CharacterName} at Map {player.NhanVatToaDo_BanDo}");
                LogHelper.WriteLine(LogLevel.Info,
                    $"Position: ({player.NhanVatToaDo_X}, {player.NhanVatToaDo_Y})");

                // Lấy grid và vị trí grid
                var grid = AOIManager.Instance.GetOrCreateGrid(player.NhanVatToaDo_BanDo);
                var gridPos = grid.WorldToGrid(player.NhanVatToaDo_X, player.NhanVatToaDo_Y);

                LogHelper.WriteLine(LogLevel.Info,
                    $"Grid position: [{gridPos.x},{gridPos.y}]");

                // Debug 3x3 cells xung quanh
                LogHelper.WriteLine(LogLevel.Info, "=== 3x3 Grid Analysis ===");
                int totalPlayersInGrid = 0;
                int totalNPCsInGrid = 0;
                int totalItemsInGrid = 0;

                for (int x = gridPos.x - 1; x <= gridPos.x + 1; x++)
                {
                    for (int y = gridPos.y - 1; y <= gridPos.y + 1; y++)
                    {
                        var cell = grid.GetCell(x, y);
                        if (cell != null)
                        {
                            var stats = cell.GetStats();
                            totalPlayersInGrid += stats.players;
                            totalNPCsInGrid += stats.npcs;
                            totalItemsInGrid += stats.items;

                            LogHelper.WriteLine(LogLevel.Info,
                                $"Cell [{x},{y}]: P:{stats.players} N:{stats.npcs} I:{stats.items}");

                            // List NPCs trong cell
                            if (stats.npcs > 0)
                            {
                                var npcs = cell.GetNPCs();
                                foreach (var npc in npcs)
                                {
                                    var distance = Math.Sqrt(
                                        Math.Pow(player.NhanVatToaDo_X - npc.Rxjh_X, 2) +
                                        Math.Pow(player.NhanVatToaDo_Y - npc.Rxjh_Y, 2));
                                    LogHelper.WriteLine(LogLevel.Info,
                                        $"  NPC {npc.FLD_PID} at ({npc.Rxjh_X:F1}, {npc.Rxjh_Y:F1}) dist:{distance:F1}");
                                }
                            }
                        }
                        else
                        {
                            LogHelper.WriteLine(LogLevel.Info, $"Cell [{x},{y}]: NULL");
                        }
                    }
                }

                LogHelper.WriteLine(LogLevel.Info,
                    $"Total in 3x3 grid: Players:{totalPlayersInGrid} NPCs:{totalNPCsInGrid} Items:{totalItemsInGrid}");

                // Test AOI queries
                var nearbyPlayers = AOIManager.Instance.GetNearbyPlayers(player);
                var nearbyNPCs = AOIManager.Instance.GetNearbyNPCs(player);
                var nearbyItems = AOIManager.Instance.GetNearbyItems(player);

                LogHelper.WriteLine(LogLevel.Info,
                    $"AOI Query Results: Players:{nearbyPlayers.Count} NPCs:{nearbyNPCs.Count} Items:{nearbyItems.Count}");

                // So sánh với player's current lists
                LogHelper.WriteLine(LogLevel.Info, "=== Current Player Lists ===");
                LogHelper.WriteLine(LogLevel.Info,
                    $"Player.PlayList: {player.PlayList?.Count ?? 0}");
                LogHelper.WriteLine(LogLevel.Info,
                    $"Player.NpcList: {player.NpcList?.Count ?? 0}");
                LogHelper.WriteLine(LogLevel.Info,
                    $"Player.ListOfGroundItems: {player.ListOfGroundItems?.Count ?? 0}");

                LogHelper.WriteLine(LogLevel.Info, "=== AOI Debug Complete ===");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in DebugPlayerAOI: {ex.Message}");
            }
        }

        /// <summary>
        /// Debug NPCs trên toàn bộ bản đồ
        /// </summary>
        public static void DebugMapNPCs(int mapId)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"=== Map {mapId} NPC Debug ===");

                // Kiểm tra trong World.MapList
                if (World.MapList.TryGetValue(mapId, out var mapClass))
                {
                    LogHelper.WriteLine(LogLevel.Info,
                        $"World.MapList[{mapId}] contains {mapClass.npcTemplate.Count} NPCs");

                    var grid = AOIManager.Instance.GetOrCreateGrid(mapId);
                    var gridStats = grid.GetStats();

                    LogHelper.WriteLine(LogLevel.Info,
                        $"AOI Grid stats: Cells:{gridStats.totalCells} NPCs:{gridStats.totalNPCs}");

                    // List một số NPCs
                    int count = 0;
                    foreach (var npc in mapClass.npcTemplate.Values)
                    {
                        if (count >= 10) break; // Chỉ hiển thị 10 NPCs đầu tiên

                        var npcGridPos = grid.WorldToGrid(npc.Rxjh_X, npc.Rxjh_Y);
                        LogHelper.WriteLine(LogLevel.Info,
                            $"NPC {npc.FLD_PID} ({npc.Name}) at ({npc.Rxjh_X:F1}, {npc.Rxjh_Y:F1}) -> Grid [{npcGridPos.x},{npcGridPos.y}]");
                        count++;
                    }

                    if (mapClass.npcTemplate.Count > 10)
                    {
                        LogHelper.WriteLine(LogLevel.Info, $"... and {mapClass.npcTemplate.Count - 10} more NPCs");
                    }
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"Map {mapId} not found in World.MapList");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in DebugMapNPCs: {ex.Message}");
            }
        }

        /// <summary>
        /// Kiểm tra xem NPCs có được thêm vào AOI đúng cách không
        /// </summary>
        public static void VerifyNPCsInAOI(int mapId)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"=== Verifying NPCs in AOI for Map {mapId} ===");

                if (!World.MapList.TryGetValue(mapId, out var mapClass))
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"Map {mapId} not found");
                    return;
                }

                var grid = AOIManager.Instance.GetOrCreateGrid(mapId);
                int npcsInWorld = mapClass.npcTemplate.Count;
                int npcsInAOI = grid.GetStats().totalNPCs;

                LogHelper.WriteLine(LogLevel.Info,
                    $"NPCs in World.MapList: {npcsInWorld}");
                LogHelper.WriteLine(LogLevel.Info,
                    $"NPCs in AOI Grid: {npcsInAOI}");

                if (npcsInWorld != npcsInAOI)
                {
                    LogHelper.WriteLine(LogLevel.Warning,
                        $"MISMATCH: {npcsInWorld - npcsInAOI} NPCs missing from AOI!");

                    // Tìm NPCs bị thiếu
                    foreach (var npc in mapClass.npcTemplate.Values)
                    {
                        var npcGridPos = grid.WorldToGrid(npc.Rxjh_X, npc.Rxjh_Y);
                        var cell = grid.GetCell(npcGridPos.x, npcGridPos.y);

                        if (cell == null || !cell.GetNPCs().Contains(npc))
                        {
                            LogHelper.WriteLine(LogLevel.Warning,
                                $"Missing NPC {npc.FLD_PID} at ({npc.Rxjh_X}, {npc.Rxjh_Y})");

                            // Thử thêm lại vào AOI
                            npc.JoinAOI();
                            LogHelper.WriteLine(LogLevel.Info,
                                $"Re-added NPC {npc.FLD_PID} to AOI");
                        }
                    }
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Info, "All NPCs are correctly in AOI!");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in VerifyNPCsInAOI: {ex.Message}");
            }
        }

        /// <summary>
        /// Test performance của AOI system
        /// </summary>
        public static void TestAOIPerformance(Players testPlayer, int iterations = 100)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"=== AOI Performance Test ({iterations} iterations) ===");

                var startTime = DateTime.Now;

                for (int i = 0; i < iterations; i++)
                {
                    var nearbyPlayers = AOIManager.Instance.GetNearbyPlayers(testPlayer);
                    var nearbyNPCs = AOIManager.Instance.GetNearbyNPCs(testPlayer);
                    var nearbyItems = AOIManager.Instance.GetNearbyItems(testPlayer);
                }

                var endTime = DateTime.Now;
                var totalMs = (endTime - startTime).TotalMilliseconds;
                var avgMs = totalMs / iterations;

                LogHelper.WriteLine(LogLevel.Info,
                    $"Total time: {totalMs:F2}ms, Average per query: {avgMs:F2}ms");
                LogHelper.WriteLine(LogLevel.Info,
                    $"Queries per second: {1000 / avgMs:F0}");

                // Lấy stats
                var stats = AOIManager.Instance.GetOverallStats();
                LogHelper.WriteLine(LogLevel.Info, $"AOI Stats: {stats}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in TestAOIPerformance: {ex.Message}");
            }
        }

        /// <summary>
        /// Enable debug logging cho AOI
        /// </summary>
        public static void EnableDebugLogging()
        {
            AOIConfig.EnableDebugLogging = true;
            LogHelper.WriteLine(LogLevel.Info, "AOI Debug logging enabled");
        }

        /// <summary>
        /// Disable debug logging cho AOI
        /// </summary>
        public static void DisableDebugLogging()
        {
            AOIConfig.EnableDebugLogging = false;
            LogHelper.WriteLine(LogLevel.Info, "AOI Debug logging disabled");
        }

        /// <summary>
        /// Hiển thị cấu hình AOI hiện tại
        /// </summary>
        public static void ShowAOIConfig()
        {
            LogHelper.WriteLine(LogLevel.Info, "=== AOI Configuration ===");
            LogHelper.WriteLine(LogLevel.Info, $"GridCellSize: {AOIConfig.GridCellSize}");
            LogHelper.WriteLine(LogLevel.Info, $"DefaultPlayerRange: {AOIConfig.DefaultPlayerRange}");
            LogHelper.WriteLine(LogLevel.Info, $"DefaultNPCRange: {AOIConfig.DefaultNPCRange}");
            LogHelper.WriteLine(LogLevel.Info, $"DefaultItemRange: {AOIConfig.DefaultItemRange}");
            LogHelper.WriteLine(LogLevel.Info, $"EnableDebugLogging: {AOIConfig.EnableDebugLogging}");
            LogHelper.WriteLine(LogLevel.Info, $"UseDirtyFlagging: {AOIConfig.UseDirtyFlagging}");
            LogHelper.WriteLine(LogLevel.Info, $"UseBatchUpdates: {AOIConfig.UseBatchUpdates}");
        }

        /// <summary>
        /// Debug chi tiết grid boundary issues
        /// </summary>
        public static void DebugGridBoundary(Players player)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"=== Grid Boundary Debug for {player.CharacterName} ===");

                var grid = AOIManager.Instance.GetOrCreateGrid(player.NhanVatToaDo_BanDo);
                var playerGridPos = grid.WorldToGrid(player.NhanVatToaDo_X, player.NhanVatToaDo_Y);

                LogHelper.WriteLine(LogLevel.Info,
                    $"Player at ({player.NhanVatToaDo_X:F1}, {player.NhanVatToaDo_Y:F1}) -> Grid [{playerGridPos.x},{playerGridPos.y}]");

                // Kiểm tra tất cả players trong 3x3 grid
                LogHelper.WriteLine(LogLevel.Info, "=== Players in 3x3 Grid ===");
                for (int x = playerGridPos.x - 1; x <= playerGridPos.x + 1; x++)
                {
                    for (int y = playerGridPos.y - 1; y <= playerGridPos.y + 1; y++)
                    {
                        var cell = grid.GetCell(x, y);
                        if (cell != null)
                        {
                            var playersInCell = cell.GetPlayers();
                            LogHelper.WriteLine(LogLevel.Info,
                                $"Cell [{x},{y}]: {playersInCell.Count} players");

                            foreach (var otherPlayer in playersInCell)
                            {
                                var distance = Math.Sqrt(
                                    Math.Pow(player.NhanVatToaDo_X - otherPlayer.NhanVatToaDo_X, 2) +
                                    Math.Pow(player.NhanVatToaDo_Y - otherPlayer.NhanVatToaDo_Y, 2));

                                var otherGridPos = grid.WorldToGrid(otherPlayer.NhanVatToaDo_X, otherPlayer.NhanVatToaDo_Y);
                                LogHelper.WriteLine(LogLevel.Info,
                                    $"  Player {otherPlayer.CharacterName} at ({otherPlayer.NhanVatToaDo_X:F1}, {otherPlayer.NhanVatToaDo_Y:F1}) " +
                                    $"Grid [{otherGridPos.x},{otherGridPos.y}] Distance: {distance:F1}");
                            }
                        }
                    }
                }

                // Test AOI query
                var nearbyPlayers = AOIManager.Instance.GetNearbyPlayers(player);
                LogHelper.WriteLine(LogLevel.Info, $"AOI Query found {nearbyPlayers.Count} nearby players");

                // Kiểm tra player's current PlayList
                LogHelper.WriteLine(LogLevel.Info, $"Player.PlayList contains {player.PlayList?.Count ?? 0} players");

                player.HeThongNhacNho($"Grid boundary debug completed. Check logs for details.", 7, "");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in DebugGridBoundary: {ex.Message}");
            }
        }

        /// <summary>
        /// Force refresh tất cả NPCs trong AOI cho một bản đồ
        /// </summary>
        public static void ForceRefreshAllNPCs(int mapId)
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, $"=== Force Refresh NPCs for Map {mapId} ===");

                if (!World.MapList.TryGetValue(mapId, out var mapClass))
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"Map {mapId} not found");
                    return;
                }

                var grid = AOIManager.Instance.GetOrCreateGrid(mapId);

                // Xóa tất cả NPCs khỏi AOI
                grid.Clear();

                // Thêm lại tất cả NPCs
                int addedCount = 0;
                foreach (var npc in mapClass.npcTemplate.Values)
                {
                    try
                    {
                        npc.JoinAOI();
                        addedCount++;
                    }
                    catch (Exception ex)
                    {
                        LogHelper.WriteLine(LogLevel.Error, $"Error adding NPC {npc.FLD_PID} to AOI: {ex.Message}");
                    }
                }

                LogHelper.WriteLine(LogLevel.Info, $"Force refresh completed. Added {addedCount} NPCs to AOI");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in ForceRefreshAllNPCs: {ex.Message}");
            }
        }

        /// <summary>
        /// Command để test AOI system
        /// </summary>
        public static void ProcessAOICommand(Players player, string command)
        {
            try
            {
                var parts = command.Split(' ');
                if (parts.Length < 2) return;

                switch (parts[1].ToLower())
                {
                    case "debug":
                        DebugPlayerAOI(player);
                        break;

                    case "verify":
                        if (parts.Length >= 3 && int.TryParse(parts[2], out int mapId))
                        {
                            VerifyNPCsInAOI(mapId);
                        }
                        else
                        {
                            VerifyNPCsInAOI(player.NhanVatToaDo_BanDo);
                        }
                        break;

                    case "mapnpcs":
                        if (parts.Length >= 3 && int.TryParse(parts[2], out int mapId2))
                        {
                            DebugMapNPCs(mapId2);
                        }
                        else
                        {
                            DebugMapNPCs(player.NhanVatToaDo_BanDo);
                        }
                        break;

                    case "perf":
                        if (parts.Length >= 3 && int.TryParse(parts[2], out int iterations))
                        {
                            TestAOIPerformance(player, iterations);
                        }
                        else
                        {
                            TestAOIPerformance(player, 100);
                        }
                        break;

                    case "config":
                        ShowAOIConfig();
                        break;

                    case "enable":
                        EnableDebugLogging();
                        player.HeThongNhacNho("AOI Debug logging enabled", 7, "");
                        break;

                    case "disable":
                        DisableDebugLogging();
                        player.HeThongNhacNho("AOI Debug logging disabled", 7, "");
                        break;

                    case "refresh":
                        player.RefreshAOI();
                        player.HeThongNhacNho("AOI refreshed (incremental)", 7, "");
                        break;

                    case "fullrefresh":
                        player.ForceRefreshAOI();
                        player.HeThongNhacNho("AOI force refreshed (full)", 7, "");
                        break;

                    case "stats":
                        var stats = AOIExtensions.GetAOIStats();
                        player.HeThongNhacNho($"AOI Stats: {stats}", 7, "");
                        break;

                    case "grid":
                        DebugGridBoundary(player);
                        break;

                    case "force":
                        ForceRefreshAllNPCs(player.NhanVatToaDo_BanDo);
                        player.HeThongNhacNho("Force refreshed all NPCs in AOI", 7, "");
                        break;

                    default:
                        player.HeThongNhacNho("AOI Commands: debug, verify [mapid], mapnpcs [mapid], perf [iterations], config, enable, disable, refresh, fullrefresh, stats, grid, force", 7, "");
                        break;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error processing AOI command: {ex.Message}");
                player.HeThongNhacNho($"AOI Command error: {ex.Message}", 7, "");
            }
        }
    }
}
