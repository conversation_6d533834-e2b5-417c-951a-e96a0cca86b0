using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;
using RxjhServer;

namespace HeroYulgang.RxjhServer.AOI
{
    /// <summary>
    /// Quản lý grid AOI cho một bản đồ cụ thể
    /// Chia bản đồ thành các cells để tối ưu việc tìm kiếm entities
    /// </summary>
    public class AOIGrid
    {
        /// <summary>
        /// ID của bản đồ
        /// </summary>
        public int MapID { get; private set; }

        /// <summary>
        /// Kích thước mỗi grid cell
        /// </summary>
        public int GridCellSize { get; private set; }

        /// <summary>
        /// Dictionary chứa tất cả các cells trong grid
        /// Key: (x, y) - vị trí grid
        /// Value: AOICell
        /// </summary>
        private readonly Dictionary<(int x, int y), AOICell> _cells;

        /// <summary>
        /// Cache để lưu trữ kết quả tìm kiếm gần đây
        /// </summary>
        private readonly Dictionary<string, (DateTime timestamp, object result)> _cache;

        /// <summary>
        /// Khóa để đảm bảo thread safety
        /// </summary>
        private readonly object _lock = new object();

        /// <summary>
        /// Thời gian tạo grid
        /// </summary>
        public DateTime CreatedTime { get; private set; }

        public AOIGrid(int mapID, int gridCellSize = 200)
        {
            MapID = mapID;
            GridCellSize = gridCellSize;
            _cells = new Dictionary<(int x, int y), AOICell>();
            _cache = new Dictionary<string, (DateTime, object)>();
            CreatedTime = DateTime.Now;

            if (AOIConfig.EnableDebugLogging)
            {
                LogHelper.WriteLine(LogLevel.Info, $"AOIGrid created for Map {mapID} with cell size {gridCellSize}");
            }
        }

        /// <summary>
        /// Chuyển đổi tọa độ world sang tọa độ grid
        /// </summary>
        public (int x, int y) WorldToGrid(float worldX, float worldY)
        {
            int gridX = (int)Math.Floor(worldX / GridCellSize);
            int gridY = (int)Math.Floor(worldY / GridCellSize);
            return (gridX, gridY);
        }

        /// <summary>
        /// Chuyển đổi tọa độ grid sang tọa độ world (center của cell)
        /// </summary>
        public (float x, float y) GridToWorld(int gridX, int gridY)
        {
            float worldX = gridX * GridCellSize + GridCellSize / 2f;
            float worldY = gridY * GridCellSize + GridCellSize / 2f;
            return (worldX, worldY);
        }

        /// <summary>
        /// Lấy hoặc tạo cell tại vị trí grid
        /// </summary>
        public AOICell GetOrCreateCell(int gridX, int gridY)
        {
            lock (_lock)
            {
                var key = (gridX, gridY);
                if (!_cells.TryGetValue(key, out AOICell cell))
                {
                    cell = new AOICell(gridX, gridY);
                    _cells[key] = cell;

                    if (AOIConfig.EnableDebugLogging)
                    {
                        LogHelper.WriteLine(LogLevel.Debug, $"Created new AOICell at [{gridX},{gridY}] for Map {MapID}");
                    }
                }
                return cell;
            }
        }

        /// <summary>
        /// Lấy cell tại vị trí grid (không tạo mới)
        /// </summary>
        public AOICell GetCell(int gridX, int gridY)
        {
            lock (_lock)
            {
                _cells.TryGetValue((gridX, gridY), out AOICell cell);
                return cell;
            }
        }

        /// <summary>
        /// Lấy tất cả cells xung quanh một vị trí trong phạm vi
        /// </summary>
        public List<AOICell> GetNeighborCells(float worldX, float worldY, int range)
        {
            var result = new List<AOICell>();
            var centerGrid = WorldToGrid(worldX, worldY);

            // Tính số cells cần kiểm tra dựa trên range
            int cellRange = (int)Math.Ceiling((double)range / GridCellSize);

            lock (_lock)
            {
                for (int x = centerGrid.x - cellRange; x <= centerGrid.x + cellRange; x++)
                {
                    for (int y = centerGrid.y - cellRange; y <= centerGrid.y + cellRange; y++)
                    {
                        var cell = GetCell(x, y);
                        if (cell != null && !cell.IsEmpty())
                        {
                            result.Add(cell);
                        }
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Thêm player vào grid
        /// </summary>
        public void AddPlayer(Players player)
        {
            var gridPos = WorldToGrid(player.NhanVatToaDo_X, player.NhanVatToaDo_Y);
            var cell = GetOrCreateCell(gridPos.x, gridPos.y);
            cell.AddPlayer(player);

            if (AOIConfig.EnableDebugLogging)
            {
                LogHelper.WriteLine(LogLevel.Debug, $"Added Player {player.CharacterName} to cell [{gridPos.x},{gridPos.y}]");
            }
        }

        /// <summary>
        /// Xóa player khỏi grid
        /// </summary>
        public void RemovePlayer(Players player, float lastX, float lastY)
        {
            var gridPos = WorldToGrid(lastX, lastY);
            var cell = GetCell(gridPos.x, gridPos.y);
            if (cell != null)
            {
                cell.RemovePlayer(player);

                if (AOIConfig.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, $"Removed Player {player.CharacterName} from cell [{gridPos.x},{gridPos.y}]");
                }

                // Dọn dẹp cell rỗng
                if (cell.IsEmpty())
                {
                    RemoveEmptyCell(gridPos.x, gridPos.y);
                }
            }
        }

        /// <summary>
        /// Cập nhật vị trí player trong grid
        /// </summary>
        public void UpdatePlayerPosition(Players player, float oldX, float oldY, float newX, float newY)
        {
            var oldGrid = WorldToGrid(oldX, oldY);
            var newGrid = WorldToGrid(newX, newY);

            // Nếu không đổi cell thì không cần làm gì
            if (oldGrid.x == newGrid.x && oldGrid.y == newGrid.y)
                return;

            // Xóa khỏi cell cũ
            var oldCell = GetCell(oldGrid.x, oldGrid.y);
            if (oldCell != null)
            {
                oldCell.RemovePlayer(player);
                if (oldCell.IsEmpty())
                {
                    RemoveEmptyCell(oldGrid.x, oldGrid.y);
                }
            }

            // Thêm vào cell mới
            var newCell = GetOrCreateCell(newGrid.x, newGrid.y);
            newCell.AddPlayer(player);

            // Xóa cache liên quan
            InvalidateCache();

            if (AOIConfig.EnableDebugLogging)
            {
                LogHelper.WriteLine(LogLevel.Debug,
                    $"Moved Player {player.CharacterName} from cell [{oldGrid.x},{oldGrid.y}] to [{newGrid.x},{newGrid.y}]");
            }
        }

        /// <summary>
        /// Thêm NPC vào grid
        /// </summary>
        public void AddNPC(NpcClass npc)
        {
            var gridPos = WorldToGrid(npc.Rxjh_X, npc.Rxjh_Y);
            var cell = GetOrCreateCell(gridPos.x, gridPos.y);
            cell.AddNPC(npc);
        }

        /// <summary>
        /// Xóa NPC khỏi grid
        /// </summary>
        public void RemoveNPC(NpcClass npc)
        {
            var gridPos = WorldToGrid(npc.Rxjh_X, npc.Rxjh_Y);
            var cell = GetCell(gridPos.x, gridPos.y);
            if (cell != null)
            {
                cell.RemoveNPC(npc);
                if (cell.IsEmpty())
                {
                    RemoveEmptyCell(gridPos.x, gridPos.y);
                }
            }
        }

        /// <summary>
        /// Thêm item vào grid
        /// </summary>
        public void AddItem(X_Mat_Dat_Vat_Pham_Loai item)
        {
            var gridPos = WorldToGrid(item.Rxjh_X, item.Rxjh_Y);
            var cell = GetOrCreateCell(gridPos.x, gridPos.y);
            cell.AddItem(item);
        }

        /// <summary>
        /// Xóa item khỏi grid
        /// </summary>
        public void RemoveItem(X_Mat_Dat_Vat_Pham_Loai item)
        {
            var gridPos = WorldToGrid(item.Rxjh_X, item.Rxjh_Y);
            var cell = GetCell(gridPos.x, gridPos.y);
            if (cell != null)
            {
                cell.RemoveItem(item);
                if (cell.IsEmpty())
                {
                    RemoveEmptyCell(gridPos.x, gridPos.y);
                }
            }
        }

        /// <summary>
        /// Xóa cell rỗng để tiết kiệm memory
        /// </summary>
        private void RemoveEmptyCell(int gridX, int gridY)
        {
            lock (_lock)
            {
                var key = (gridX, gridY);
                if (_cells.TryGetValue(key, out AOICell cell) && cell.IsEmpty())
                {
                    _cells.Remove(key);
                }
            }
        }

        /// <summary>
        /// Xóa cache cũ
        /// </summary>
        private void InvalidateCache()
        {
            if (!AOIConfig.UseDirtyFlagging) return;

            lock (_lock)
            {
                var now = DateTime.Now;
                var keysToRemove = _cache.Where(kvp =>
                    (now - kvp.Value.timestamp).TotalMilliseconds > AOIConfig.CacheTimeoutMs)
                    .Select(kvp => kvp.Key).ToList();

                foreach (var key in keysToRemove)
                {
                    _cache.Remove(key);
                }
            }
        }

        /// <summary>
        /// Lấy thống kê về grid
        /// </summary>
        public (int totalCells, int totalPlayers, int totalNPCs, int totalItems) GetStats()
        {
            lock (_lock)
            {
                int totalCells = _cells.Count;
                int totalPlayers = 0;
                int totalNPCs = 0;
                int totalItems = 0;

                foreach (var cell in _cells.Values)
                {
                    var stats = cell.GetStats();
                    totalPlayers += stats.players;
                    totalNPCs += stats.npcs;
                    totalItems += stats.items;
                }

                return (totalCells, totalPlayers, totalNPCs, totalItems);
            }
        }

        /// <summary>
        /// Dọn dẹp toàn bộ grid
        /// </summary>
        public void Clear()
        {
            lock (_lock)
            {
                foreach (var cell in _cells.Values)
                {
                    cell.Clear();
                }
                _cells.Clear();
                _cache.Clear();
            }
        }

        public override string ToString()
        {
            var stats = GetStats();
            return $"AOIGrid[Map:{MapID}] - Cells: {stats.totalCells}, Players: {stats.totalPlayers}, NPCs: {stats.totalNPCs}, Items: {stats.totalItems}";
        }
    }
}
