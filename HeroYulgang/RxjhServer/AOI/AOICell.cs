using System;
using System.Collections.Generic;
using System.Linq;
using RxjhServer;

namespace HeroYulgang.RxjhServer.AOI
{
    /// <summary>
    /// Đại diện cho một cell trong grid AOI
    /// Chứa tất cả entities (players, NPCs, items) trong vùng này
    /// </summary>
    public class AOICell
    {
        /// <summary>
        /// Vị trí grid của cell này
        /// </summary>
        public (int x, int y) GridPosition { get; set; }

        /// <summary>
        /// Danh sách players trong cell này
        /// </summary>
        public HashSet<Players> Players { get; private set; }

        /// <summary>
        /// Danh sách NPCs trong cell này
        /// </summary>
        public HashSet<NpcClass> NPCs { get; private set; }

        /// <summary>
        /// Danh sách items trong cell này
        /// </summary>
        public HashSet<X_Mat_Dat_Vat_Pham_Loai> Items { get; private set; }

        /// <summary>
        /// Thời gian cập nhật cuối cùng
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// Flag đánh dấu cell này đã bị thay đổi và cần update
        /// </summary>
        public bool IsDirty { get; set; }

        /// <summary>
        /// Khóa để đảm bảo thread safety
        /// </summary>
        private readonly object _lock = new object();

        public AOICell(int gridX, int gridY)
        {
            GridPosition = (gridX, gridY);
            Players = new HashSet<Players>();
            NPCs = new HashSet<NpcClass>();
            Items = new HashSet<X_Mat_Dat_Vat_Pham_Loai>();
            LastUpdateTime = DateTime.Now;
            IsDirty = false;
        }

        /// <summary>
        /// Thêm player vào cell
        /// </summary>
        public bool AddPlayer(Players player)
        {
            lock (_lock)
            {
                bool added = Players.Add(player);
                if (added)
                {
                    IsDirty = true;
                    LastUpdateTime = DateTime.Now;
                }
                return added;
            }
        }

        /// <summary>
        /// Xóa player khỏi cell
        /// </summary>
        public bool RemovePlayer(Players player)
        {
            lock (_lock)
            {
                bool removed = Players.Remove(player);
                if (removed)
                {
                    IsDirty = true;
                    LastUpdateTime = DateTime.Now;
                }
                return removed;
            }
        }

        /// <summary>
        /// Thêm NPC vào cell
        /// </summary>
        public bool AddNPC(NpcClass npc)
        {
            lock (_lock)
            {
                bool added = NPCs.Add(npc);
                if (added)
                {
                    IsDirty = true;
                    LastUpdateTime = DateTime.Now;
                }
                return added;
            }
        }

        /// <summary>
        /// Xóa NPC khỏi cell
        /// </summary>
        public bool RemoveNPC(NpcClass npc)
        {
            lock (_lock)
            {
                bool removed = NPCs.Remove(npc);
                if (removed)
                {
                    IsDirty = true;
                    LastUpdateTime = DateTime.Now;
                }
                return removed;
            }
        }

        /// <summary>
        /// Thêm item vào cell
        /// </summary>
        public bool AddItem(X_Mat_Dat_Vat_Pham_Loai item)
        {
            lock (_lock)
            {
                bool added = Items.Add(item);
                if (added)
                {
                    IsDirty = true;
                    LastUpdateTime = DateTime.Now;
                }
                return added;
            }
        }

        /// <summary>
        /// Xóa item khỏi cell
        /// </summary>
        public bool RemoveItem(X_Mat_Dat_Vat_Pham_Loai item)
        {
            lock (_lock)
            {
                bool removed = Items.Remove(item);
                if (removed)
                {
                    IsDirty = true;
                    LastUpdateTime = DateTime.Now;
                }
                return removed;
            }
        }

        /// <summary>
        /// Lấy tất cả players trong cell (thread-safe copy)
        /// </summary>
        public List<Players> GetPlayers()
        {
            lock (_lock)
            {
                return Players.ToList();
            }
        }

        /// <summary>
        /// Lấy tất cả NPCs trong cell (thread-safe copy)
        /// </summary>
        public List<NpcClass> GetNPCs()
        {
            lock (_lock)
            {
                return NPCs.ToList();
            }
        }

        /// <summary>
        /// Lấy tất cả items trong cell (thread-safe copy)
        /// </summary>
        public List<X_Mat_Dat_Vat_Pham_Loai> GetItems()
        {
            lock (_lock)
            {
                return Items.ToList();
            }
        }

        /// <summary>
        /// Kiểm tra cell có rỗng không
        /// </summary>
        public bool IsEmpty()
        {
            lock (_lock)
            {
                return Players.Count == 0 && NPCs.Count == 0 && Items.Count == 0;
            }
        }

        /// <summary>
        /// Đặt lại dirty flag
        /// </summary>
        public void ClearDirtyFlag()
        {
            lock (_lock)
            {
                IsDirty = false;
            }
        }

        /// <summary>
        /// Lấy thống kê về cell
        /// </summary>
        public (int players, int npcs, int items) GetStats()
        {
            lock (_lock)
            {
                return (Players.Count, NPCs.Count, Items.Count);
            }
        }

        /// <summary>
        /// Dọn dẹp cell - xóa tất cả entities
        /// </summary>
        public void Clear()
        {
            lock (_lock)
            {
                Players.Clear();
                NPCs.Clear();
                Items.Clear();
                IsDirty = true;
                LastUpdateTime = DateTime.Now;
            }
        }

        public override string ToString()
        {
            var stats = GetStats();
            return $"AOICell[{GridPosition.x},{GridPosition.y}] - Players: {stats.players}, NPCs: {stats.npcs}, Items: {stats.items}";
        }
    }
}
