using System;
using System.Collections.Generic;
using System.Linq;
using System.Collections.Concurrent;
using HeroYulgang.Helpers;
using RxjhServer;

namespace HeroYulgang.RxjhServer.AOI
{
    /// <summary>
    /// Tracker để quản lý visibility changes và chỉ update khi cần thiết
    /// Tránh việc update lặp lại không cần thiết khi player di chuyển giữa các cells
    /// </summary>
    public class AOIVisibilityTracker
    {
        /// <summary>
        /// Lưu trữ danh sách entities mà player đang nhìn thấy
        /// Key: PlayerSessionID, Value: Set of visible entity IDs
        /// </summary>
        private readonly ConcurrentDictionary<int, HashSet<int>> _playerVisiblePlayers;
        private readonly ConcurrentDictionary<int, HashSet<int>> _playerVisibleNPCs;
        private readonly ConcurrentDictionary<int, HashSet<long>> _playerVisibleItems;

        /// <summary>
        /// Lưu trữ vị trí grid cuối cùng của players
        /// Key: PlayerSessionID, Value: (gridX, gridY)
        /// </summary>
        private readonly ConcurrentDictionary<int, (int x, int y)> _playerLastGridPositions;

        /// <summary>
        /// Lock objects cho thread safety
        /// </summary>
        private readonly object _playersLock = new object();
        private readonly object _npcsLock = new object();
        private readonly object _itemsLock = new object();

        public AOIVisibilityTracker()
        {
            _playerVisiblePlayers = new ConcurrentDictionary<int, HashSet<int>>();
            _playerVisibleNPCs = new ConcurrentDictionary<int, HashSet<int>>();
            _playerVisibleItems = new ConcurrentDictionary<int, HashSet<long>>();
            _playerLastGridPositions = new ConcurrentDictionary<int, (int, int)>();
        }

        /// <summary>
        /// Cập nhật visibility cho players với incremental updates
        /// </summary>
        public void UpdatePlayerVisibility(Players player, List<Players> currentNearbyPlayers)
        {
            lock (_playersLock)
            {
                var playerId = player.SessionID;
                var currentVisibleIds = currentNearbyPlayers.Select(p => p.SessionID).ToHashSet();

                // Lấy danh sách visible trước đó
                if (!_playerVisiblePlayers.TryGetValue(playerId, out var previousVisibleIds))
                {
                    previousVisibleIds = new HashSet<int>();
                    _playerVisiblePlayers[playerId] = previousVisibleIds;
                }

                // Tìm players cần thêm (mới nhìn thấy)
                var playersToAdd = currentVisibleIds.Except(previousVisibleIds).ToList();
                
                // Tìm players cần xóa (không còn nhìn thấy)
                var playersToRemove = previousVisibleIds.Except(currentVisibleIds).ToList();

                if (AOIConfig.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, 
                        $"AOI Visibility: {player.CharacterName} - Add: {playersToAdd.Count}, Remove: {playersToRemove.Count}");
                }

                // Xử lý players cần xóa
                foreach (var playerIdToRemove in playersToRemove)
                {
                    if (World.allConnectedChars.TryGetValue(playerIdToRemove, out var playerToRemove))
                    {
                        // Xóa khỏi PlayList của player hiện tại
                        var playerId_Server = (World.ServerID, playerIdToRemove);
                        if (player.PlayList?.ContainsKey(playerId_Server) == true)
                        {
                            player.PlayList.Remove(playerId_Server);
                            player.DiChuyen_RaKhoi_BanDo(player, playerToRemove);
                        }

                        // Xóa player hiện tại khỏi PlayList của player kia
                        var currentPlayerId_Server = (World.ServerID, playerId);
                        if (playerToRemove.PlayList?.ContainsKey(currentPlayerId_Server) == true)
                        {
                            playerToRemove.PlayList.Remove(currentPlayerId_Server);
                            playerToRemove.DiChuyen_RaKhoi_BanDo(playerToRemove, player);
                        }
                    }
                }

                // Xử lý players cần thêm
                foreach (var playerIdToAdd in playersToAdd)
                {
                    if (World.allConnectedChars.TryGetValue(playerIdToAdd, out var playerToAdd))
                    {
                        // Thêm vào PlayList của player hiện tại
                        var playerId_Server = (World.ServerID, playerIdToAdd);
                        if (player.PlayList != null && !player.PlayList.ContainsKey(playerId_Server))
                        {
                            player.PlayList.Add(playerId_Server, playerToAdd);
                            player.UpdateCharacterData(playerToAdd);
                        }

                        // Thêm player hiện tại vào PlayList của player kia
                        var currentPlayerId_Server = (World.ServerID, playerId);
                        if (playerToAdd.PlayList != null && !playerToAdd.PlayList.ContainsKey(currentPlayerId_Server))
                        {
                            playerToAdd.PlayList.Add(currentPlayerId_Server, player);
                            playerToAdd.UpdateCharacterData(player);
                        }

                        // Xử lý visibility effects
                        HandlePlayerVisibilityEffects(player, playerToAdd);
                    }
                }

                // Cập nhật danh sách visible
                _playerVisiblePlayers[playerId] = currentVisibleIds;
            }
        }

        /// <summary>
        /// Cập nhật visibility cho NPCs với incremental updates
        /// </summary>
        public void UpdateNPCVisibility(Players player, List<NpcClass> currentNearbyNPCs)
        {
            lock (_npcsLock)
            {
                var playerId = player.SessionID;
                var currentVisibleIds = currentNearbyNPCs.Select(npc => npc.NPC_SessionID).ToHashSet();

                // Lấy danh sách visible trước đó
                if (!_playerVisibleNPCs.TryGetValue(playerId, out var previousVisibleIds))
                {
                    previousVisibleIds = new HashSet<int>();
                    _playerVisibleNPCs[playerId] = previousVisibleIds;
                }

                // Tìm NPCs cần thêm và xóa
                var npcsToAdd = currentVisibleIds.Except(previousVisibleIds).ToList();
                var npcsToRemove = previousVisibleIds.Except(currentVisibleIds).ToList();

                if (AOIConfig.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, 
                        $"AOI NPC Visibility: {player.CharacterName} - Add: {npcsToAdd.Count}, Remove: {npcsToRemove.Count}");
                }

                // Xử lý NPCs cần xóa
                var npcsToRemoveDict = new Dictionary<int, NpcClass>();
                foreach (var npcIdToRemove in npcsToRemove)
                {
                    if (player.NpcList?.TryGetValue(npcIdToRemove, out var npcToRemove) == true)
                    {
                        player.NpcList.Remove(npcIdToRemove);
                        npcsToRemoveDict[npcIdToRemove] = npcToRemove;
                        
                        if (npcToRemove.Contains(player))
                        {
                            npcToRemove.PlayList_Remove(player);
                        }
                    }
                }

                // Xử lý NPCs cần thêm
                var npcsToAddList = new List<NpcClass>();
                foreach (var npcIdToAdd in npcsToAdd)
                {
                    var npcToAdd = currentNearbyNPCs.FirstOrDefault(npc => npc.NPC_SessionID == npcIdToAdd);
                    if (npcToAdd != null)
                    {
                        if (player.NpcList != null && !player.NpcList.ContainsKey(npcIdToAdd))
                        {
                            player.NpcList.Add(npcIdToAdd, npcToAdd);
                            npcToAdd.PlayList_Add(player);
                            npcsToAddList.Add(npcToAdd);
                        }
                    }
                }

                // Gửi updates tới client
                if (npcsToRemoveDict.Count > 0)
                {
                    NpcClass.UpdateNPC_DeXoaSoLieu(npcsToRemoveDict, player);
                }

                if (npcsToAddList.Count > 0)
                {
                    foreach (var npc in npcsToAddList)
                    {
                        npc.UpdateNPCSoLieu(player);
                    }
                }

                // Cập nhật danh sách visible
                _playerVisibleNPCs[playerId] = currentVisibleIds;
            }
        }

        /// <summary>
        /// Cập nhật visibility cho Items với incremental updates
        /// </summary>
        public void UpdateItemVisibility(Players player, List<X_Mat_Dat_Vat_Pham_Loai> currentNearbyItems)
        {
            lock (_itemsLock)
            {
                var playerId = player.SessionID;
                var currentVisibleIds = currentNearbyItems.Select(item => item.id).ToHashSet();

                // Lấy danh sách visible trước đó
                if (!_playerVisibleItems.TryGetValue(playerId, out var previousVisibleIds))
                {
                    previousVisibleIds = new HashSet<long>();
                    _playerVisibleItems[playerId] = previousVisibleIds;
                }

                // Tìm items cần thêm và xóa
                var itemsToAdd = currentVisibleIds.Except(previousVisibleIds).ToList();
                var itemsToRemove = previousVisibleIds.Except(currentVisibleIds).ToList();

                if (AOIConfig.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, 
                        $"AOI Item Visibility: {player.CharacterName} - Add: {itemsToAdd.Count}, Remove: {itemsToRemove.Count}");
                }

                // Xử lý items cần xóa
                foreach (var itemIdToRemove in itemsToRemove)
                {
                    if (player.ListOfGroundItems?.TryGetValue(itemIdToRemove, out var itemToRemove) == true)
                    {
                        player.ListOfGroundItems.Remove(itemIdToRemove);
                        itemToRemove.PlayList?.Remove(playerId);
                        
                        // Gửi lệnh xóa item khỏi client
                        SendRemoveGroundItemData(player, itemToRemove);
                    }
                }

                // Xử lý items cần thêm
                foreach (var itemIdToAdd in itemsToAdd)
                {
                    var itemToAdd = currentNearbyItems.FirstOrDefault(item => item.id == itemIdToAdd);
                    if (itemToAdd != null)
                    {
                        if (player.ListOfGroundItems != null && !player.ListOfGroundItems.ContainsKey(itemIdToAdd))
                        {
                            player.ListOfGroundItems.Add(itemIdToAdd, itemToAdd);
                            itemToAdd.PlayList?.Add(playerId, player);
                            
                            // Gửi thông tin item tới client
                            SendGroundItemData(player, itemToAdd);
                        }
                    }
                }

                // Cập nhật danh sách visible
                _playerVisibleItems[playerId] = currentVisibleIds;
            }
        }

        /// <summary>
        /// Kiểm tra xem player có thay đổi grid không
        /// </summary>
        public bool HasPlayerChangedGrid(Players player)
        {
            var grid = AOIManager.Instance.GetOrCreateGrid(player.NhanVatToaDo_BanDo);
            var currentGridPos = grid.WorldToGrid(player.NhanVatToaDo_X, player.NhanVatToaDo_Y);

            if (_playerLastGridPositions.TryGetValue(player.SessionID, out var lastGridPos))
            {
                bool hasChanged = lastGridPos.x != currentGridPos.x || lastGridPos.y != currentGridPos.y;
                
                if (hasChanged)
                {
                    _playerLastGridPositions[player.SessionID] = currentGridPos;
                    
                    if (AOIConfig.EnableDebugLogging)
                    {
                        LogHelper.WriteLine(LogLevel.Debug, 
                            $"Player {player.CharacterName} changed grid from [{lastGridPos.x},{lastGridPos.y}] to [{currentGridPos.x},{currentGridPos.y}]");
                    }
                }
                
                return hasChanged;
            }
            else
            {
                _playerLastGridPositions[player.SessionID] = currentGridPos;
                return true; // First time, consider as changed
            }
        }

        /// <summary>
        /// Xóa player khỏi tracker khi logout
        /// </summary>
        public void RemovePlayer(int playerId)
        {
            _playerVisiblePlayers.TryRemove(playerId, out _);
            _playerVisibleNPCs.TryRemove(playerId, out _);
            _playerVisibleItems.TryRemove(playerId, out _);
            _playerLastGridPositions.TryRemove(playerId, out _);
        }

        /// <summary>
        /// Xử lý hiệu ứng visibility (GM mode, stealth, etc.)
        /// </summary>
        private void HandlePlayerVisibilityEffects(Players viewer, Players target)
        {
            try
            {
                if (target.GMMode != 0 && target.HinhThuc_TangHinh != 0)
                {
                    target.HinhThuc_TangHinh = 1;
                    target.CheDo_TangHinh(1);
                }

                if (viewer.GMMode != 0 && viewer.HinhThuc_TangHinh != 0)
                {
                    viewer.HinhThuc_TangHinh = 1;
                    viewer.CheDo_TangHinh(1);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error handling visibility effects: {ex.Message}");
            }
        }

        /// <summary>
        /// Helper method để gửi thông tin ground item tới client
        /// </summary>
        private void SendGroundItemData(Players player, X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                // TODO: Implement item packet sending logic
                // Tạm thời để trống, sẽ implement sau khi có thêm thông tin về packet structure
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error sending ground item data: {ex.Message}");
            }
        }

        /// <summary>
        /// Helper method để gửi lệnh xóa ground item khỏi client
        /// </summary>
        private void SendRemoveGroundItemData(Players player, X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                // TODO: Implement item removal packet sending logic
                // Tạm thời để trống, sẽ implement sau khi có thêm thông tin về packet structure
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error sending remove ground item data: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy thống kê về visibility tracker
        /// </summary>
        public (int players, int npcs, int items) GetStats()
        {
            return (_playerVisiblePlayers.Count, _playerVisibleNPCs.Count, _playerVisibleItems.Count);
        }

        /// <summary>
        /// Dọn dẹp toàn bộ tracker
        /// </summary>
        public void Clear()
        {
            _playerVisiblePlayers.Clear();
            _playerVisibleNPCs.Clear();
            _playerVisibleItems.Clear();
            _playerLastGridPositions.Clear();
        }
    }
}
