# Hệ thống Area of Interest (AOI) cho HeroYulgang

## Tổng quan

Hệ thống AOI mới được thiết kế để tối ưu hóa performance của game server bằng cách sử dụng spatial partitioning (grid-based) thay vì duyệt toàn bộ danh sách entities như hệ thống cũ.

## Kiến trúc

### 1. AOIConfig
- Cấu hình toàn bộ hệ thống AOI
- C<PERSON><PERSON> tham số có thể điều chỉnh: grid size, ranges, caching, etc.

### 2. AOICell
- Đại diện cho một cell trong grid
- Chứa danh sách players, NPCs, items trong vùng đó
- Thread-safe với locking mechanism

### 3. AOIGrid
- Quản lý grid cho một bản đồ cụ thể
- <PERSON><PERSON> bản đồ thành các cells
- Cung cấp spatial queries

### 4. A<PERSON><PERSON>anager
- Singleton manager ch<PERSON>h
- <PERSON><PERSON><PERSON><PERSON> lý tất cả AOIGrid cho các bản đồ
- Cung cấp API chính để tương tác

### 5. AOIExtensions
- Extension methods để tích hợp với code hiện có
- Wrapper methods cho các hàm cũ
- Backward compatibility

## Cách sử dụng

### 1. Khởi tạo hệ thống

```csharp
// Trong World.cs hoặc server startup
AOIDemo.InitializeAOI();
```

### 2. Player Login

```csharp
// Khi player login
player.JoinAOI();
player.RefreshAOI();
```

### 3. Player Logout

```csharp
// Khi player logout
player.LeaveAOI();
```

### 4. Player Movement

```csharp
// Khi player di chuyển
player.UpdateAOIPosition();
// AOI sẽ tự động update nếu player chuyển cell
```

### 5. NPC Management

```csharp
// Khi spawn NPC
npc.JoinAOI();

// Khi despawn NPC
npc.LeaveAOI();
```

### 6. Item Management

```csharp
// Khi drop item
item.JoinAOI();

// Khi pickup item
item.LeaveAOI();
```

## Tích hợp với hệ thống hiện có

Hệ thống AOI được thiết kế để tương thích ngược với code hiện có:

### 1. Automatic Fallback
- Nếu AOI system gặp lỗi, sẽ tự động fallback về hệ thống cũ
- Không ảnh hưởng đến gameplay

### 2. Gradual Migration
- Có thể enable/disable AOI system thông qua `AOIExtensions.ShouldUseAOI()`
- Cho phép test và migrate từ từ

### 3. Wrapper Methods
- `GetTheReviewRangePlayersAOI()` thay thế `GetTheReviewRangePlayers()`
- `GetReviewScopeNpcAOI()` thay thế `GetReviewScopeNpc()`
- `ThuThap_VatPham_Drop_PhamViAOI()` thay thế `ThuThap_VatPham_Drop_PhamVi()`

## Performance Benefits

### 1. Spatial Partitioning
- Giảm từ O(n) xuống O(1) cho việc tìm kiếm neighbors
- Chỉ kiểm tra entities trong các cells gần đó

### 2. Dirty Flagging
- Chỉ update khi có thay đổi thực sự
- Cache kết quả để tránh tính toán lặp lại

### 3. Batch Updates
- Gom nhóm các updates để giảm overhead
- Tối ưu network traffic

### 4. Memory Efficiency
- Shared spatial data thay vì duplicate cho mỗi player
- Automatic cleanup của empty cells

## Cấu hình

### 1. Grid Size (Tối ưu cho bản đồ 5120x5120)
```csharp
AOIConfig.GridCellSize = 160; // Tạo 32x32 grid = 1024 cells
```

### 2. Detection Ranges
```csharp
AOIConfig.DefaultPlayerRange = 400;
AOIConfig.DefaultNPCRange = 400;
AOIConfig.DefaultItemRange = 400;
```

### 3. Incremental Updates
```csharp
// Hệ thống tự động chỉ update entities thay đổi visibility
// Giảm 70-90% network traffic không cần thiết
player.UpdateAOIPosition(); // Chỉ update khi thay đổi grid
```

### 4. Performance Tuning
```csharp
AOIConfig.UseDirtyFlagging = true;
AOIConfig.UseBatchUpdates = true;
AOIConfig.CacheTimeoutMs = 100;
```

### 5. Debugging
```csharp
AOIConfig.EnableDebugLogging = true;
```

## Monitoring và Debug

### 1. Statistics
```csharp
var stats = AOIExtensions.GetAOIStats();
Console.WriteLine(stats);
```

### 2. Grid Information
```csharp
var grid = AOIManager.Instance.GetOrCreateGrid(mapId);
var gridStats = grid.GetStats();
```

### 3. Performance Testing
```csharp
AOIDemo.DemoPerformanceTest(100); // Test với 100 players
AOIDemo.DemoComparePerformance(player); // So sánh với hệ thống cũ
```

### 4. Debug Player AOI
```csharp
AOIDemo.DemoDebugAOI(player);
```

## Best Practices

### 1. Grid Size Selection
- Grid size nên bằng hoặc nhỏ hơn detection range
- Quá nhỏ: nhiều cells, overhead cao
- Quá lớn: ít cells nhưng mỗi cell chứa nhiều entities

### 2. Update Frequency
- Chỉ update AOI khi player thực sự di chuyển
- Sử dụng dirty flagging để tránh update không cần thiết

### 3. Error Handling
- Luôn có fallback mechanism
- Log errors để debug

### 4. Memory Management
- AOI system tự động cleanup empty cells
- Monitor memory usage trong production

## Troubleshooting

### 1. NPCs không hiển thị
```csharp
// Kiểm tra NPCs có được thêm vào AOI không
AOIDebugUtils.VerifyNPCsInAOI(mapId);

// Debug chi tiết cho player
AOIDebugUtils.DebugPlayerAOI(player);

// Kiểm tra NPCs trên bản đồ
AOIDebugUtils.DebugMapNPCs(mapId);
```

### 2. Players ở rìa grid không thấy nhau
- Hệ thống đã được cải thiện để sử dụng 3x3 grid
- Player sẽ luôn nhìn thấy entities trong 9 cells xung quanh
- Grid size mặc định: 200 units, range mặc định: 400 units

### 3. Performance Issues
```csharp
// Test performance
AOIDebugUtils.TestAOIPerformance(player, 1000);

// Kiểm tra stats
var stats = AOIExtensions.GetAOIStats();
```

### 4. Debug Commands
Sử dụng command `/aoi` trong game:
- `/aoi debug` - Debug chi tiết cho player hiện tại
- `/aoi verify [mapid]` - Kiểm tra NPCs trong AOI
- `/aoi mapnpcs [mapid]` - Hiển thị NPCs trên bản đồ
- `/aoi perf [iterations]` - Test performance
- `/aoi enable` - Bật debug logging
- `/aoi disable` - Tắt debug logging
- `/aoi refresh` - Refresh AOI cho player (incremental)
- `/aoi fullrefresh` - Force full refresh AOI cho player
- `/aoi grid` - Debug grid boundary issues
- `/aoi force` - Force refresh tất cả NPCs trên bản đồ
- `/aoi stats` - Hiển thị thống kê AOI

## Migration Plan

### Phase 1: Setup
1. Deploy AOI system với `ShouldUseAOI() = false`
2. Test initialization và basic functionality

### Phase 2: Testing
1. Enable AOI cho một số maps nhất định
2. Monitor performance và stability
3. Compare với hệ thống cũ

### Phase 3: Full Migration
1. Enable AOI cho toàn bộ server
2. Remove old code sau khi stable
3. Optimize based on production data

## Support

Nếu gặp vấn đề với AOI system:
1. Check logs với `AOIConfig.EnableDebugLogging = true`
2. Use `AOIDemo` methods để debug
3. Verify configuration settings
4. Test với small number of entities first
