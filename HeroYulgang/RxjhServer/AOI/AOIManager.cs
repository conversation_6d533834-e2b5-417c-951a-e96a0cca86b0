using System;
using System.Collections.Generic;
using System.Linq;
using System.Collections.Concurrent;
using HeroYulgang.Helpers;
using RxjhServer;

namespace HeroYulgang.RxjhServer.AOI
{
    /// <summary>
    /// Manager ch<PERSON>h cho hệ thống Area of Interest
    /// Qu<PERSON>n lý tất cả AOIGrid cho các bản đồ và cung cấp API để tương tác
    /// </summary>
    public class AOIManager
    {
        private static AOIManager _instance;
        private static readonly object _lockInstance = new object();

        /// <summary>
        /// Singleton instance
        /// </summary>
        public static AOIManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lockInstance)
                    {
                        if (_instance == null)
                        {
                            _instance = new AOIManager();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Dictionary chứa AOIGrid cho mỗi bản đồ
        /// Key: MapID, Value: AOIGrid
        /// </summary>
        private readonly ConcurrentDictionary<int, AOIGrid> _mapGrids;

        /// <summary>
        /// Lưu trữ vị trí cuối cùng của players để tracking movement
        /// Key: SessionID, Value: (MapID, X, Y)
        /// </summary>
        private readonly ConcurrentDictionary<int, (int mapId, float x, float y)> _playerLastPositions;

        /// <summary>
        /// Queue cho batch updates
        /// </summary>
        private readonly Queue<Action> _batchUpdateQueue;
        private readonly object _batchLock = new object();

        /// <summary>
        /// Thống kê performance
        /// </summary>
        public AOIStats Stats { get; private set; }

        /// <summary>
        /// Visibility tracker để quản lý incremental updates
        /// </summary>
        private readonly AOIVisibilityTracker _visibilityTracker;

        private AOIManager()
        {
            _mapGrids = new ConcurrentDictionary<int, AOIGrid>();
            _playerLastPositions = new ConcurrentDictionary<int, (int, float, float)>();
            _batchUpdateQueue = new Queue<Action>();
            _visibilityTracker = new AOIVisibilityTracker();
            Stats = new AOIStats();

            LogHelper.WriteLine(LogLevel.Info, "AOIManager initialized");
        }

        /// <summary>
        /// Lấy hoặc tạo AOIGrid cho bản đồ
        /// </summary>
        public AOIGrid GetOrCreateGrid(int mapId)
        {
            return _mapGrids.GetOrAdd(mapId, id => new AOIGrid(id, AOIConfig.GridCellSize));
        }

        /// <summary>
        /// Thêm player vào hệ thống AOI
        /// </summary>
        public void AddPlayer(Players player)
        {
            try
            {
                var grid = GetOrCreateGrid(player.NhanVatToaDo_BanDo);
                grid.AddPlayer(player);

                // Lưu vị trí hiện tại
                _playerLastPositions[player.SessionID] = (
                    player.NhanVatToaDo_BanDo,
                    player.NhanVatToaDo_X,
                    player.NhanVatToaDo_Y
                );

                Stats.TotalPlayersAdded++;

                if (AOIConfig.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug,
                        $"AOI: Added player {player.CharacterName} to map {player.NhanVatToaDo_BanDo}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error adding player {player.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa player khỏi hệ thống AOI
        /// </summary>
        public void RemovePlayer(Players player)
        {
            try
            {
                if (_playerLastPositions.TryRemove(player.SessionID, out var lastPos))
                {
                    var grid = GetOrCreateGrid(lastPos.mapId);
                    grid.RemovePlayer(player, lastPos.x, lastPos.y);

                    // Xóa khỏi visibility tracker
                    _visibilityTracker.RemovePlayer(player.SessionID);

                    Stats.TotalPlayersRemoved++;

                    if (AOIConfig.EnableDebugLogging)
                    {
                        LogHelper.WriteLine(LogLevel.Debug,
                            $"AOI: Removed player {player.CharacterName} from map {lastPos.mapId}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error removing player {player.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Cập nhật vị trí player
        /// </summary>
        public void UpdatePlayerPosition(Players player)
        {
            try
            {
                var currentPos = (player.NhanVatToaDo_BanDo, player.NhanVatToaDo_X, player.NhanVatToaDo_Y);

                if (_playerLastPositions.TryGetValue(player.SessionID, out var lastPos))
                {
                    // Kiểm tra xem có thay đổi bản đồ không
                    if (lastPos.mapId != currentPos.NhanVatToaDo_BanDo)
                    {
                        // Chuyển bản đồ - xóa khỏi bản đồ cũ và thêm vào bản đồ mới
                        var oldGrid = GetOrCreateGrid(lastPos.mapId);
                        oldGrid.RemovePlayer(player, lastPos.x, lastPos.y);

                        var newGrid = GetOrCreateGrid(currentPos.NhanVatToaDo_BanDo);
                        newGrid.AddPlayer(player);
                    }
                    else
                    {
                        // Cùng bản đồ - chỉ cập nhật vị trí
                        var grid = GetOrCreateGrid(currentPos.NhanVatToaDo_BanDo);
                        grid.UpdatePlayerPosition(player, lastPos.x, lastPos.y,
                            currentPos.NhanVatToaDo_X, currentPos.NhanVatToaDo_Y);
                    }
                }
                else
                {
                    // Player mới - thêm vào hệ thống
                    AddPlayer(player);
                    return;
                }

                // Cập nhật vị trí cuối cùng
                _playerLastPositions[player.SessionID] = currentPos;
                Stats.TotalPositionUpdates++;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error updating player position {player.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy danh sách players xung quanh một player
        /// </summary>
        public List<Players> GetNearbyPlayers(Players player, int range = -1)
        {
            if (range == -1) range = AOIConfig.DefaultPlayerRange;

            try
            {
                var grid = GetOrCreateGrid(player.NhanVatToaDo_BanDo);
                var nearbyPlayers = new List<Players>();

                var neighborCells = grid.GetNeighborCells(player.NhanVatToaDo_X, player.NhanVatToaDo_Y, range);

                foreach (var cell in neighborCells)
                {
                    var cellPlayers = cell.GetPlayers();
                    foreach (var otherPlayer in cellPlayers)
                    {
                        if (otherPlayer.SessionID == player.SessionID) continue;

                        // Kiểm tra khoảng cách chính xác
                        if (IsInRange(player.NhanVatToaDo_X, player.NhanVatToaDo_Y,
                                    otherPlayer.NhanVatToaDo_X, otherPlayer.NhanVatToaDo_Y, range))
                        {
                            // Kiểm tra Zone visibility nếu có
                            if (CanSeePlayer(player, otherPlayer))
                            {
                                nearbyPlayers.Add(otherPlayer);
                            }
                        }
                    }
                }

                Stats.TotalPlayerQueries++;
                return nearbyPlayers;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error getting nearby players for {player.CharacterName}: {ex.Message}");
                return new List<Players>();
            }
        }

        /// <summary>
        /// Lấy danh sách NPCs xung quanh một player
        /// </summary>
        public List<NpcClass> GetNearbyNPCs(Players player, int range = -1)
        {
            if (range == -1) range = AOIConfig.DefaultNPCRange;

            try
            {
                var grid = GetOrCreateGrid(player.NhanVatToaDo_BanDo);
                var nearbyNPCs = new List<NpcClass>();

                var neighborCells = grid.GetNeighborCells(player.NhanVatToaDo_X, player.NhanVatToaDo_Y, range);

                if (AOIConfig.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug,
                        $"AOI: GetNearbyNPCs for {player.CharacterName} - Checking {neighborCells.Count} cells");
                }

                foreach (var cell in neighborCells)
                {
                    var cellNPCs = cell.GetNPCs();

                    if (AOIConfig.EnableDebugLogging && cellNPCs.Count > 0)
                    {
                        LogHelper.WriteLine(LogLevel.Debug,
                            $"AOI: Cell [{cell.GridPosition.x},{cell.GridPosition.y}] has {cellNPCs.Count} NPCs");
                    }

                    foreach (var npc in cellNPCs)
                    {
                        // Kiểm tra khoảng cách chính xác
                        if (IsInRange(player.NhanVatToaDo_X, player.NhanVatToaDo_Y,
                                    npc.Rxjh_X, npc.Rxjh_Y, range))
                        {
                            // Kiểm tra Zone visibility nếu có
                            if (CanSeeNPC(player, npc))
                            {
                                nearbyNPCs.Add(npc);

                                if (AOIConfig.EnableDebugLogging)
                                {
                                    LogHelper.WriteLine(LogLevel.Debug,
                                        $"AOI: Added NPC {npc.FLD_PID} to nearby list for {player.CharacterName}");
                                }
                            }
                        }
                    }
                }

                Stats.TotalNPCQueries++;

                if (AOIConfig.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug,
                        $"AOI: Found {nearbyNPCs.Count} nearby NPCs for {player.CharacterName}");
                }

                return nearbyNPCs;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error getting nearby NPCs for {player.CharacterName}: {ex.Message}");
                return new List<NpcClass>();
            }
        }

        /// <summary>
        /// Lấy danh sách items xung quanh một player
        /// </summary>
        public List<X_Mat_Dat_Vat_Pham_Loai> GetNearbyItems(Players player, int range = -1)
        {
            if (range == -1) range = AOIConfig.DefaultItemRange;

            try
            {
                var grid = GetOrCreateGrid(player.NhanVatToaDo_BanDo);
                var nearbyItems = new List<X_Mat_Dat_Vat_Pham_Loai>();

                var neighborCells = grid.GetNeighborCells(player.NhanVatToaDo_X, player.NhanVatToaDo_Y, range);

                foreach (var cell in neighborCells)
                {
                    var cellItems = cell.GetItems();
                    foreach (var item in cellItems)
                    {
                        // Kiểm tra khoảng cách chính xác
                        if (IsInRange(player.NhanVatToaDo_X, player.NhanVatToaDo_Y,
                                    item.Rxjh_X, item.Rxjh_Y, range))
                        {
                            // Kiểm tra Zone visibility nếu có
                            if (CanSeeItem(player, item))
                            {
                                nearbyItems.Add(item);
                            }
                        }
                    }
                }

                Stats.TotalItemQueries++;
                return nearbyItems;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error getting nearby items for {player.CharacterName}: {ex.Message}");
                return new List<X_Mat_Dat_Vat_Pham_Loai>();
            }
        }

        /// <summary>
        /// Thêm NPC vào hệ thống AOI
        /// </summary>
        public void AddNPC(NpcClass npc)
        {
            try
            {
                var grid = GetOrCreateGrid(npc.Rxjh_Map);
                grid.AddNPC(npc);
                Stats.TotalNPCsAdded++;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error adding NPC {npc.FLD_PID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa NPC khỏi hệ thống AOI
        /// </summary>
        public void RemoveNPC(NpcClass npc)
        {
            try
            {
                var grid = GetOrCreateGrid(npc.Rxjh_Map);
                grid.RemoveNPC(npc);
                Stats.TotalNPCsRemoved++;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error removing NPC {npc.FLD_PID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Thêm item vào hệ thống AOI
        /// </summary>
        public void AddItem(X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                var grid = GetOrCreateGrid(item.Rxjh_Map);
                grid.AddItem(item);
                Stats.TotalItemsAdded++;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error adding item {item.id}: {ex.Message}");
            }
        }

        /// <summary>
        /// Xóa item khỏi hệ thống AOI
        /// </summary>
        public void RemoveItem(X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                var grid = GetOrCreateGrid(item.Rxjh_Map);
                grid.RemoveItem(item);
                Stats.TotalItemsRemoved++;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error removing item {item.id}: {ex.Message}");
            }
        }

        /// <summary>
        /// Kiểm tra khoảng cách giữa hai điểm
        /// </summary>
        private bool IsInRange(float x1, float y1, float x2, float y2, int range)
        {
            var dx = x1 - x2;
            var dy = y1 - y2;
            return Math.Sqrt(dx * dx + dy * dy) <= range;
        }

        /// <summary>
        /// Kiểm tra player có thể nhìn thấy player khác không (Zone rules)
        /// </summary>
        private bool CanSeePlayer(Players viewer, Players target)
        {
            // Sử dụng logic Zone hiện có nếu có
            if (viewer.CurrentZone != null && target.CurrentZone != null)
            {
                return viewer.CurrentZone.CanSeeZone(target.CurrentZone);
            }
            return true;
        }

        /// <summary>
        /// Kiểm tra player có thể nhìn thấy NPC không (Zone rules)
        /// </summary>
        private bool CanSeeNPC(Players viewer, NpcClass npc)
        {
            // Sử dụng logic Zone hiện có nếu có
            if (viewer.CurrentZone != null && npc.CurrentZone != null)
            {
                return viewer.CurrentZone.CanSeeZone(npc.CurrentZone);
            }
            return true;
        }

        /// <summary>
        /// Kiểm tra player có thể nhìn thấy item không (Zone rules)
        /// </summary>
        private bool CanSeeItem(Players viewer, X_Mat_Dat_Vat_Pham_Loai item)
        {
            // Sử dụng logic Zone hiện có nếu có
            if (viewer.CurrentZone != null && item.CurrentZone != null)
            {
                return viewer.CurrentZone.CanSeeZone(item.CurrentZone);
            }
            return true;
        }

        /// <summary>
        /// Lấy thống kê tổng quan
        /// </summary>
        public string GetOverallStats()
        {
            var totalStats = new
            {
                TotalMaps = _mapGrids.Count,
                TotalPlayers = _playerLastPositions.Count,
                Stats.TotalPlayersAdded,
                Stats.TotalPlayersRemoved,
                Stats.TotalPositionUpdates,
                Stats.TotalPlayerQueries,
                Stats.TotalNPCQueries,
                Stats.TotalItemQueries
            };

            return $"AOI Stats: {totalStats}";
        }

        /// <summary>
        /// Cập nhật visibility với incremental updates (chỉ update khi cần thiết)
        /// </summary>
        public void UpdatePlayerVisibilityIncremental(Players player)
        {
            try
            {
                // Kiểm tra xem player có thay đổi grid không
                if (!_visibilityTracker.HasPlayerChangedGrid(player))
                {
                    // Không thay đổi grid, không cần update
                    return;
                }

                // Lấy entities xung quanh
                var nearbyPlayers = GetNearbyPlayers(player);
                var nearbyNPCs = GetNearbyNPCs(player);
                var nearbyItems = GetNearbyItems(player);

                // Sử dụng visibility tracker để chỉ update những thay đổi
                _visibilityTracker.UpdatePlayerVisibility(player, nearbyPlayers);
                _visibilityTracker.UpdateNPCVisibility(player, nearbyNPCs);
                _visibilityTracker.UpdateItemVisibility(player, nearbyItems);

                if (AOIConfig.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug,
                        $"AOI: Incremental update for {player.CharacterName} - Players: {nearbyPlayers.Count}, NPCs: {nearbyNPCs.Count}, Items: {nearbyItems.Count}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error in incremental update for {player.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Force full refresh cho player (không sử dụng incremental)
        /// </summary>
        public void ForceFullRefresh(Players player)
        {
            try
            {
                // Xóa visibility history để force full update
                _visibilityTracker.RemovePlayer(player.SessionID);

                // Trigger full update
                UpdatePlayerVisibilityIncremental(player);

                if (AOIConfig.EnableDebugLogging)
                {
                    LogHelper.WriteLine(LogLevel.Debug, $"AOI: Force full refresh for {player.CharacterName}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AOI: Error in force refresh for {player.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Dọn dẹp hệ thống
        /// </summary>
        public void Cleanup()
        {
            foreach (var grid in _mapGrids.Values)
            {
                grid.Clear();
            }
            _mapGrids.Clear();
            _playerLastPositions.Clear();
            _visibilityTracker.Clear();

            lock (_batchLock)
            {
                _batchUpdateQueue.Clear();
            }

            LogHelper.WriteLine(LogLevel.Info, "AOIManager cleaned up");
        }
    }

    /// <summary>
    /// Class để lưu trữ thống kê performance của AOI system
    /// </summary>
    public class AOIStats
    {
        public long TotalPlayersAdded { get; set; }
        public long TotalPlayersRemoved { get; set; }
        public long TotalNPCsAdded { get; set; }
        public long TotalNPCsRemoved { get; set; }
        public long TotalItemsAdded { get; set; }
        public long TotalItemsRemoved { get; set; }
        public long TotalPositionUpdates { get; set; }
        public long TotalPlayerQueries { get; set; }
        public long TotalNPCQueries { get; set; }
        public long TotalItemQueries { get; set; }
    }
}
